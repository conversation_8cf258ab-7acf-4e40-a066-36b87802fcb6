use crate::types::{MarketData, Order, SubscriptionType, Trade};
use crate::websocket::handler::{
    BinanceBookTicker, BinanceDepthUpdate, BinanceOrderData, BinanceOrderTradeUpdate, BinanceTrade,
    StreamWrapper, WebSocketMessage,
};
use crate::websocket::subscription::SubscriptionManager;
use crate::{BacktestError, Result};
use std::sync::Arc;
use tokio::sync::broadcast;
use tracing::{debug, error, info, warn};

/// WebSocket数据分发器
/// 负责接收撮合引擎转发的市场数据并分发给订阅的客户端
pub struct WebSocketDistributor {
    /// 市场数据接收器
    market_data_rx: broadcast::Receiver<MarketData>,
    /// 订单更新接收器
    order_update_rx: Option<broadcast::Receiver<Order>>,
    trade_rx: Option<broadcast::Receiver<Trade>>,
    /// 订阅管理器
    subscription_manager: Arc<SubscriptionManager>,
}

impl WebSocketDistributor {
    /// 创建新的WebSocket数据分发器
    pub fn new(
        market_data_rx: broadcast::Receiver<MarketData>,
        subscription_manager: Arc<SubscriptionManager>,
    ) -> Self {
        Self {
            market_data_rx,
            order_update_rx: None,
            trade_rx: None,
            subscription_manager,
        }
    }

    /// 设置订单更新接收器
    pub fn set_order_update_receiver(&mut self, order_update_rx: broadcast::Receiver<Order>) {
        self.order_update_rx = Some(order_update_rx);
    }

    pub fn set_trade_receiver(&mut self, trade_rx: broadcast::Receiver<Trade>) {
        self.trade_rx = Some(trade_rx);
    }

    /// 启动数据分发
    pub async fn start_distribution(&mut self) -> Result<()> {
        info!("Starting WebSocket data distribution");

        loop {
            tokio::select! {
                // 处理市场数据
                market_data_result = self.market_data_rx.recv() => {
                    match market_data_result {
                        Ok(market_data) => {
                            if let Err(e) = self.distribute_market_data(market_data).await {
                                error!("Failed to distribute market data: {}", e);
                                continue;
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Market data channel closed");
                            break;
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            debug!("Market data lagged, skipped {} messages", skipped);
                        }
                    }
                }

                // 处理订单更新（如果有的话）
                order_update_result = async {
                    if let Some(ref mut order_rx) = self.order_update_rx {
                        order_rx.recv().await
                    } else {
                        // 如果没有订单更新接收器，永远等待
                        std::future::pending().await
                    }
                } => {
                    info!("Received order update {:?}", order_update_result);
                    match order_update_result {
                        Ok(order) => {
                            if let Err(e) = self.distribute_order_update(order).await {
                                error!("Failed to distribute order update: {}", e);
                                continue;
                            }
                        }
                        Err(broadcast::error::RecvError::Closed) => {
                            info!("Order update channel closed");
                            // 不退出，继续处理市场数据
                        }
                        Err(broadcast::error::RecvError::Lagged(skipped)) => {
                            debug!("Order updates lagged, skipped {} messages", skipped);
                        }
                    }
                }
            }
        }

        info!("WebSocket data distribution stopped");
        Ok(())
    }

    /// 分发市场数据给订阅的客户端
    async fn distribute_market_data(&self, market_data: MarketData) -> Result<()> {
        let subscription_type = self.get_subscription_type(&market_data);

        // 检查是否有客户端订阅了这种数据类型
        let subscriber_count = self
            .subscription_manager
            .subscription_count(&subscription_type);
        if subscriber_count == 0 {
            debug!(
                "No subscribers for {:?}, skipping distribution",
                subscription_type
            );
            return Ok(());
        }

        // 转换为Binance格式并发送
        if let Some(binance_data) = self.convert_to_binance_format(&market_data) {
            let message_json = serde_json::to_string(&binance_data).map_err(|e| {
                BacktestError::WebSocket(format!("Failed to serialize Binance data: {}", e))
            })?;

            // 根据数据类型生成正确的流名称
            let stream_name = self.get_stream_name(&market_data);
            let stream_data = StreamWrapper {
                stream: stream_name,
                data: serde_json::from_str(&message_json).unwrap(),
            };

            let message_json = serde_json::to_string(&stream_data).unwrap();

            // 广播给所有订阅的客户端
            self.subscription_manager
                .broadcast(&subscription_type, message_json)
                .await;

            debug!(
                "Distributed {:?} data to {} subscribers (Binance format)",
                subscription_type, subscriber_count
            );
        } else {
            // 回退到原有格式（保持向后兼容）
            let data_json = serde_json::to_value(&market_data).map_err(|e| {
                BacktestError::WebSocket(format!("Failed to serialize market data: {}", e))
            })?;

            let ws_message = WebSocketMessage::Data {
                subscription: subscription_type.clone(),
                data: data_json,
            };

            let message_json = serde_json::to_string(&ws_message).map_err(|e| {
                BacktestError::WebSocket(format!("Failed to serialize WebSocket message: {}", e))
            })?;

            self.subscription_manager
                .broadcast(&subscription_type, message_json)
                .await;

            debug!(
                "Distributed {:?} data to {} subscribers (legacy format)",
                subscription_type, subscriber_count
            );
        }

        Ok(())
    }

    /// 分发订单更新给相关客户端
    async fn distribute_order_update(&self, order: Order) -> Result<()> {
        info!(
            "Distributing order update: {} status={:?}",
            order.id, order.status
        );

        // 生成数字订单ID（从字符串ID中提取或生成）
        let order_id_num = order
            .id
            .chars()
            .filter(|c| c.is_ascii_digit())
            .collect::<String>()
            .parse::<u64>()
            .unwrap_or_else(|_| {
                // 如果无法解析，使用时间戳作为ID
                chrono::Utc::now().timestamp_nanos_opt().unwrap_or(0) as u64
            });

        // 获取当前时间戳（毫秒）
        let current_time = chrono::Utc::now().timestamp_millis() as u64;

        // 创建Binance格式的ORDER_TRADE_UPDATE事件
        let order_update = BinanceOrderTradeUpdate {
            event_type: "ORDER_TRADE_UPDATE".to_string(),
            event_time: current_time,
            transaction_time: current_time,
            order: BinanceOrderData {
                symbol: "BTCUSDT".to_string(), // 在实际实现中应该从订单中获取
                client_order_id: order.client_order_id.clone(),
                side: match order.side {
                    crate::types::OrderSide::Buy => "BUY".to_string(),
                    crate::types::OrderSide::Sell => "SELL".to_string(),
                },
                order_type: match order.order_type {
                    crate::types::OrderType::Market => "MARKET".to_string(),
                    crate::types::OrderType::Limit => "LIMIT".to_string(),
                },
                time_in_force: "GTC".to_string(),
                original_quantity: format!("{:.8}", order.quantity),
                original_price: order
                    .price
                    .map(|p| format!("{:.8}", p.value()))
                    .unwrap_or("0".to_string()),
                average_price: order
                    .execution_info
                    .as_ref()
                    .and_then(|info| info.average_price)
                    .map(|p| format!("{:.8}", p.value()))
                    .unwrap_or("0.0".to_string()),
                stop_price: "0".to_string(),
                execution_type: match order.status {
                    crate::types::OrderStatus::Pending => "NEW".to_string(),
                    crate::types::OrderStatus::PartiallyFilled => "TRADE".to_string(),
                    crate::types::OrderStatus::Filled => "TRADE".to_string(),
                    crate::types::OrderStatus::Cancelled => "CANCELED".to_string(),
                },
                order_status: match order.status {
                    crate::types::OrderStatus::Pending => "NEW".to_string(),
                    crate::types::OrderStatus::PartiallyFilled => "PARTIALLY_FILLED".to_string(),
                    crate::types::OrderStatus::Filled => "FILLED".to_string(),
                    crate::types::OrderStatus::Cancelled => "CANCELED".to_string(),
                },
                order_id: order_id_num,
                last_filled_quantity: order
                    .execution_info
                    .as_ref()
                    .map(|info| format!("{:.8}", info.last_filled_quantity))
                    .unwrap_or("0".to_string()),
                filled_quantity: order
                    .execution_info
                    .as_ref()
                    .map(|info| format!("{:.8}", info.filled_quantity))
                    .unwrap_or("0".to_string()),
                last_filled_price: order
                    .execution_info
                    .as_ref()
                    .and_then(|info| info.last_filled_price)
                    .map(|p| format!("{:.8}", p.value()))
                    .unwrap_or("0".to_string()),
                commission_asset: order
                    .execution_info
                    .as_ref()
                    .map(|info| info.commission_asset.clone())
                    .unwrap_or("USDT".to_string()),
                commission: order
                    .execution_info
                    .as_ref()
                    .map(|info| format!("{:.8}", info.commission))
                    .unwrap_or("0".to_string()),
                order_trade_time: current_time,
                trade_id: order
                    .execution_info
                    .as_ref()
                    .and_then(|info| info.trade_id.as_ref())
                    .and_then(|id| {
                        id.chars()
                            .filter(|c| c.is_ascii_digit())
                            .collect::<String>()
                            .parse::<u64>()
                            .ok()
                    })
                    .unwrap_or(0),
                bids_notional: "0".to_string(),
                ask_notional: "0".to_string(),
                is_maker: false,
                reduce_only: false,
                working_type: "CONTRACT_PRICE".to_string(),
                original_order_type: match order.order_type {
                    crate::types::OrderType::Market => "MARKET".to_string(),
                    crate::types::OrderType::Limit => "LIMIT".to_string(),
                },
                position_side: "BOTH".to_string(),
                close_position: false,
                activation_price: "0".to_string(),
                callback_rate: "0".to_string(),
                price_protect: false,
                ignore_si: 0,
                ignore_ss: 0,
                realized_profit: "0".to_string(),
                stp_mode: "NONE".to_string(),
                price_match_mode: "NONE".to_string(),
                gtd_auto_cancel_time: 0,
            },
        };

        // 序列化为JSON
        let message_json = serde_json::to_string(&order_update).map_err(|e| {
            BacktestError::WebSocket(format!("Failed to serialize order update: {}", e))
        })?;

        let stream_data = StreamWrapper {
            stream: "test_listen_key".to_string(),
            data: serde_json::from_str(&message_json).unwrap(),
        };

        // 直接发送给所有连接的客户端（订单更新不需要特定订阅）
        // 在实际实现中，应该只发送给下单的客户端
        self.subscription_manager
            .broadcast(
                &SubscriptionType::OrderAndFill,
                serde_json::to_string(&stream_data).unwrap(),
            )
            .await;
        info!("Order update distributed: {}", order.id);
        Ok(())
    }

    /// 根据市场数据类型获取对应的订阅类型
    fn get_subscription_type(&self, market_data: &MarketData) -> SubscriptionType {
        match market_data {
            MarketData::OrderBook(_) => SubscriptionType::OrderBook,
            MarketData::Bbo(_) => SubscriptionType::BookTicker, // Bbo映射到BookTicker订阅类型
            MarketData::Trade(_) => SubscriptionType::Trade,
            MarketData::BookTicker(_) => SubscriptionType::BookTicker,

            MarketData::TradeData(_) => SubscriptionType::Trade, // TradeData映射到AggTrade订阅类型
        }
    }

    /// 根据市场数据类型生成对应的流名称
    fn get_stream_name(&self, market_data: &MarketData) -> String {
        match market_data {
            MarketData::BookTicker(_) | MarketData::Bbo(_) => "btcusdt@bookTicker".to_string(),
            MarketData::TradeData(trade_data) => {
                format!("{}@trade", trade_data.symbol.to_lowercase())
            }
            MarketData::OrderBook(_) => "btcusdt@depth5".to_string(),
            _ => "btcusdt@bookTicker".to_string(), // 默认流名称
        }
    }

    /// 将内部MarketData转换为Binance格式
    fn convert_to_binance_format(&self, market_data: &MarketData) -> Option<serde_json::Value> {
        match market_data {
            MarketData::BookTicker(book_ticker) => {
                let binance_book_ticker = BinanceBookTicker {
                    event_type: "bookTicker".to_string(),
                    event_time: book_ticker.event_time,
                    transaction_time: book_ticker.transaction_time,
                    symbol: "BTCUSDT".to_string(), // 默认符号，实际应该从配置或数据中获取
                    update_id: book_ticker.update_id,
                    best_bid_price: format!("{:.8}", book_ticker.best_bid_price.value()),
                    best_bid_qty: format!("{:.8}", book_ticker.best_bid_qty),
                    best_ask_price: format!("{:.8}", book_ticker.best_ask_price.value()),
                    best_ask_qty: format!("{:.8}", book_ticker.best_ask_qty),
                };
                serde_json::to_value(binance_book_ticker).ok()
            }
            MarketData::OrderBook(orderbook) => {
                // 转换OrderBook为Binance Depth格式
                let mut bids = Vec::new();
                let mut asks = Vec::new();

                // 取前5个买单和卖单（模拟depth5）
                for (price, qty) in orderbook.bids.iter().take(5) {
                    bids.push([format!("{:.8}", price.value()), format!("{:.8}", qty)]);
                }

                for (price, qty) in orderbook.asks.iter().take(5) {
                    asks.push([format!("{:.8}", price.value()), format!("{:.8}", qty)]);
                }

                let binance_depth = BinanceDepthUpdate {
                    event_type: "depthUpdate".to_string(),
                    event_time: orderbook.timestamp.timestamp_millis() as u64,
                    transaction_time: orderbook.timestamp.timestamp_millis() as u64,
                    symbol: "BTCUSDT".to_string(),
                    first_update_id: orderbook.update_id.unwrap_or(0),
                    final_update_id: orderbook.update_id.unwrap_or(0),
                    prev_final_update_id: orderbook.update_id.unwrap_or(0).saturating_sub(1),
                    bids,
                    asks,
                };
                serde_json::to_value(binance_depth).ok()
            }
            MarketData::TradeData(trade_data) => {
                // 转换TradeData为Binance AggTrade格式
                let binance_agg_trade = BinanceTrade {
                    event_type: "trade".to_string(),
                    event_time: (trade_data.timestamp / 1000) as u64, // 转换微秒到毫秒
                    symbol: trade_data.symbol.to_uppercase(),
                    trade_id: trade_data.id.parse().unwrap_or(0),
                    price: format!("{:.8}", trade_data.price.value()),
                    quantity: format!("{:.8}", trade_data.amount),
                    trade_time: (trade_data.timestamp / 1000) as u64,
                    is_buyer_maker: match trade_data.side {
                        crate::types::OrderSide::Sell => true, // 卖单是maker
                        crate::types::OrderSide::Buy => false, // 买单是taker
                    },
                };
                serde_json::to_value(binance_agg_trade).ok()
            }
            MarketData::Bbo(bbo) => {
                // 转换Bbo为Binance BookTicker格式
                let binance_book_ticker = BinanceBookTicker {
                    event_type: "bookTicker".to_string(),
                    event_time: chrono::Utc::now().timestamp_millis() as u64,
                    transaction_time: chrono::Utc::now().timestamp_millis() as u64,
                    symbol: "BTCUSDT".to_string(), // 默认符号，实际应该从配置获取
                    update_id: bbo.update_id,
                    best_bid_price: format!("{:.8}", bbo.bid_price.value()),
                    best_bid_qty: format!("{:.8}", bbo.bid_quantity),
                    best_ask_price: format!("{:.8}", bbo.ask_price.value()),
                    best_ask_qty: format!("{:.8}", bbo.ask_quantity),
                };
                serde_json::to_value(binance_book_ticker).ok()
            }
            _ => None, // 其他类型暂时不支持Binance格式
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::types::{BookTicker, Price};
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_websocket_distributor() {
        let (market_data_tx, market_data_rx) = broadcast::channel(100);
        let subscription_manager = Arc::new(SubscriptionManager::new());

        let mut distributor =
            WebSocketDistributor::new(market_data_rx, subscription_manager.clone());

        // 添加一个测试客户端
        let (client_tx, mut client_rx) = mpsc::channel(100);
        let client_id = subscription_manager.add_client(client_tx);

        // 订阅BookTicker数据
        subscription_manager.subscribe(&client_id, SubscriptionType::BookTicker);

        // 启动分发器（在后台）
        let distributor_handle =
            tokio::spawn(async move { distributor.start_distribution().await });

        // 发送测试数据
        let bookticker = BookTicker {
            update_id: 12345,
            best_bid_price: Price::new(99.5),
            best_bid_qty: 10.0,
            best_ask_price: Price::new(100.5),
            best_ask_qty: 15.0,
            transaction_time: 1640995200000,
            event_time: 1640995200000,
        };

        let market_data = MarketData::BookTicker(bookticker);
        market_data_tx.send(market_data).unwrap();

        // 检查客户端是否收到数据
        let received_message = client_rx.recv().await.unwrap();
        let parsed_message: WebSocketMessage = serde_json::from_str(&received_message).unwrap();

        match parsed_message {
            WebSocketMessage::Data {
                subscription,
                data: _,
            } => {
                assert_eq!(subscription, SubscriptionType::BookTicker);
            }
            _ => panic!("Expected Data message"),
        }

        // 清理
        distributor_handle.abort();
    }
}
